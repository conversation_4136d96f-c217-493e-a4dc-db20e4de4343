import fs from "fs";
import path from "path";
import https from "https";
import http from "http";

// Comprehensive list of all image URLs found in the codebase
const imageUrls = {
  articles: [
    {
      url: "https://framerusercontent.com/images/vss98kimC7Rm3BkWtOJ4E7PF0.png?scale-down-to=512",
      filename: "ux-revolution.png",
      slug: "the-ux-revolution-shaping-digital-experiences",
    },
    {
      url: "https://framerusercontent.com/images/gEuLZWqISbowA6Z5TeEzISEsgs.jpg?scale-down-to=512",
      filename: "responsive-web-design.jpg",
      slug: "the-power-of-responsive-web-design",
    },
    {
      url: "https://framerusercontent.com/images/CteRnkedt2SHUnA2jjD4Ds7Y.jpg",
      filename: "web-design-trends-2023.jpg",
      slug: "10-web-design-trends-to-watch-in-2023",
    },
    {
      url: "https://framerusercontent.com/images/QvhBmuXxN4j9e9lYPSdADXY9mE.jpg?scale-down-to=512",
      filename: "art-responsive-design.jpg",
      slug: "the-art-of-responsive-web-design",
    },
    {
      url: "https://framerusercontent.com/images/IBw4Si6UN9kcQoOJzmLjrFRQ.jpg?scale-down-to=512",
      filename: "website-performance.jpg",
      slug: "boosting-website-performance-with-smart-design-practices",
    },
  ],
  writers: [
    {
      url: "https://framerusercontent.com/images/cfae5rVbOPJ9IKMr5XZltSR4.png",
      filename: "anastasia-solovyova.png",
      name: "Anastasia Solovyova",
    },
  ],
  services: [
    {
      url: "https://framerusercontent.com/images/SrW1Ux5MtGYoPFNopWE6a1C0.jpg",
      filename: "branding-service.jpg",
      service: "branding",
    },
  ],
  serviceIcons: [
    {
      url: "https://framerusercontent.com/images/Fp09fc507ocX7P8MZ47tH3es9ig.png",
      filename: "adobe-illustrator.png",
      alt: "Adobe Illustrator Logo",
    },
    {
      url: "https://framerusercontent.com/images/7GWO4ayVlkwffxwuedUuwm8fkz0.png",
      filename: "adobe-photoshop.png",
      alt: "Adobe Photoshop Logo",
    },
  ],
  projects: [
    {
      url: "https://framerusercontent.com/images/C46rY8czFx8HjeyGBBr7SnlupM.png",
      filename: "codify.png",
      title: "Codify",
    },
    {
      url: "https://framerusercontent.com/images/1EbsYvM5Gyb15ZgRtX8UF7b474.png",
      filename: "taskify.png",
      title: "Taskify",
    },
    {
      url: "https://framerusercontent.com/images/ymBaWuyG4ivgK2aSTlKsfHD7cvo.png",
      filename: "flexify.png",
      title: "Flexify",
    },
    {
      url: "https://framerusercontent.com/images/mt2fx3NVaLd8pGv0ERaLEJmc4Y.png",
      filename: "landify.png",
      title: "Landify",
    },
    {
      url: "https://framerusercontent.com/images/kg3eNccUy41ywZMReAJv2nO80gc.png",
      filename: "nexus-ai.png",
      title: "Nexus AI",
    },
  ],
  team: [
    {
      url: "https://framerusercontent.com/images/nFTyhTg9mtSiD0Oh51DGHixETM.jpg",
      filename: "michael-lee.jpg",
      name: "Michael Lee",
    },
    {
      url: "https://framerusercontent.com/images/3lxG5etcVf37NL3i2nIGARTTaI.jpg",
      filename: "chris-wilson.jpg",
      name: "Chris Wilson",
    },
    {
      url: "https://framerusercontent.com/images/S9PyleA1z5ugBA2Z87N0r7h5VA.jpg",
      filename: "emily-brown.jpg",
      name: "Emily Brown",
    },
    {
      url: "https://framerusercontent.com/images/rZmnnPdh2NfRFd8GwnJmVeCq5Ow.jpg",
      filename: "olena-kurcherenko.jpg",
      name: "Olena Kurcherenko",
    },
    {
      url: "https://framerusercontent.com/images/7n35wdG8jtT2LMgYCpqeBkSo6s.jpg",
      filename: "anastasia-solovyova-team.jpg",
      name: "Anastasia Solovyova",
    },
    {
      url: "https://framerusercontent.com/images/DBL6ct1qK3lvpE5gdCOeBR2CZQ.jpg",
      filename: "sarah-smith.jpg",
      name: "Sarah Smith",
    },
    {
      url: "https://framerusercontent.com/images/HH8KrojyxZx6X20z1r13CSwiiWE.jpg",
      filename: "david-johnson.jpg",
      name: "David Johnson",
    },
  ],
  testimonials: [
    {
      url: "https://framerusercontent.com/modules/PLP5SWQpFPuFrn7tLf3t/suo2OcdmUu5xmjtBXZ7I/assets/CPGgYEBeFy4gDXe5dDzh1qjQG1w.png",
      filename: "elite-fitness-owner.png",
      company: "Elite Fitness Co.",
    },
    {
      url: "https://framerusercontent.com/modules/PLP5SWQpFPuFrn7tLf3t/suo2OcdmUu5xmjtBXZ7I/assets/XfjXHJ2H7SzzlzPr1bGFm1T9BpI.png",
      filename: "greentech-manager.png",
      company: "GreenTech Solutions",
    },
  ],
  logos: [
    {
      url: "https://framerusercontent.com/images/RQtplDgO0QaR12FvioQ15mE35kU.svg",
      filename: "logo-1.svg",
    },
    {
      url: "https://framerusercontent.com/images/rim9ymYCtInC2YkivpMBMf8Dn0k.svg",
      filename: "logo-2.svg",
    },
    {
      url: "https://framerusercontent.com/images/EwqtQRJ6tQkLQz8wYeL13rdCm5E.svg",
      filename: "logo-3.svg",
    },
    {
      url: "https://framerusercontent.com/images/CgaamTpX7MhPEKWQ5nErcXoxdJc.svg",
      filename: "logo-4.svg",
    },
    {
      url: "https://framerusercontent.com/images/bD64dsBbmhkS3zkf22186FsX4Ac.svg",
      filename: "logo-5.svg",
    },
    {
      url: "https://framerusercontent.com/images/vGXtF9HXsgc9RTwZC0EnFQAc5a0.svg",
      filename: "logo-6.svg",
    },
    {
      url: "https://framerusercontent.com/images/9dwWN9t1Rvolj5dKZVTERoTo.svg",
      filename: "logo-7.svg",
    },
    {
      url: "https://framerusercontent.com/images/WiaTKEhWhhJvWfoS5B6yeBxEqo.svg",
      filename: "logo-8.svg",
    },
    {
      url: "https://framerusercontent.com/images/pWdI1S9RQGdpsjRHvWfoS5B6yeBxEqo.svg",
      filename: "logo-9.svg",
    },
    {
      url: "https://framerusercontent.com/images/MD3ZaTHRq3jFe94NbAvLnwbQPE.svg",
      filename: "logo-10.svg",
    },
  ],
  misc: [
    {
      url: "https://framerusercontent.com/images/SbtkRyLYOLa1pNM5Vb7domSNKU.png?scale-down-to=512",
      filename: "footer-logo.png",
      usage: "footer",
    },
    {
      url: "https://framerusercontent.com/images/Gy6oyLvyryjSizdl2va3wLbiYjc.png?scale-down-to=512",
      filename: "star-object.png",
      usage: "impact-statement",
    },
    {
      url: "https://framerusercontent.com/images/nVwfJfYTJH4GZwm9UXcHF6NVsr4.png?scale-down-to=512",
      filename: "spring-object.png",
      usage: "impact-statement",
    },
  ],
};

// Function to download image
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith("https:") ? https : http;

    protocol
      .get(url, (response) => {
        if (response.statusCode === 200) {
          const fileStream = fs.createWriteStream(filepath);
          response.pipe(fileStream);

          fileStream.on("finish", () => {
            fileStream.close();
            console.log(`Downloaded: ${filepath}`);
            resolve();
          });

          fileStream.on("error", (err) => {
            fs.unlink(filepath, () => {}); // Delete the file on error
            reject(err);
          });
        } else {
          reject(
            new Error(`Failed to download ${url}: ${response.statusCode}`),
          );
        }
      })
      .on("error", (err) => {
        reject(err);
      });
  });
}

// Function to download all images in a category
async function downloadCategory(category, images) {
  console.log(`\nDownloading ${category} images...`);
  const categoryPath = path.join("public", "images", category);

  for (const image of images) {
    try {
      const filepath = path.join(categoryPath, image.filename);
      await downloadImage(image.url, filepath);
    } catch (error) {
      console.error(`Error downloading ${image.url}:`, error.message);
    }
  }
}

// Main function to download all images
async function downloadAllImages() {
  console.log("Starting image download process...");

  for (const [category, images] of Object.entries(imageUrls)) {
    await downloadCategory(category, images);
  }

  console.log("\nAll images downloaded successfully!");
}

// Run the download process
downloadAllImages().catch(console.error);

import fs from "fs";
import path from "path";
import https from "https";
import http from "http";

// Remaining images to download
const remainingImages = [
  {
    url: "https://framerusercontent.com/images/cfae5rVbOPJ9IKMr5XZltSR4.png",
    filepath: "public/images/writers/anastasia-solovyova.png"
  },
  {
    url: "https://framerusercontent.com/images/Fp09fc507ocX7P8MZ47tH3es9ig.png",
    filepath: "public/images/serviceIcons/adobe-illustrator.png"
  },
  {
    url: "https://framerusercontent.com/images/7GWO4ayVlkwffxwuedUuwm8fkz0.png",
    filepath: "public/images/serviceIcons/adobe-photoshop.png"
  }
];

// Function to download image with redirect handling
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    protocol.get(url, (response) => {
      // Handle redirects
      if (response.statusCode === 301 || response.statusCode === 302) {
        const redirectUrl = response.headers.location;
        console.log(`Redirecting from ${url} to ${redirectUrl}`);
        downloadImage(redirectUrl, filepath).then(resolve).catch(reject);
        return;
      }
      
      if (response.statusCode === 200) {
        const fileStream = fs.createWriteStream(filepath);
        response.pipe(fileStream);
        
        fileStream.on('finish', () => {
          fileStream.close();
          console.log(`Downloaded: ${filepath}`);
          resolve();
        });
        
        fileStream.on('error', (err) => {
          fs.unlink(filepath, () => {}); // Delete the file on error
          reject(err);
        });
      } else {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
      }
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Download remaining images
async function downloadRemaining() {
  console.log('Downloading remaining images...');
  
  for (const image of remainingImages) {
    try {
      await downloadImage(image.url, image.filepath);
    } catch (error) {
      console.error(`Error downloading ${image.url}:`, error.message);
    }
  }
  
  console.log('Remaining images download complete!');
}

downloadRemaining().catch(console.error);
